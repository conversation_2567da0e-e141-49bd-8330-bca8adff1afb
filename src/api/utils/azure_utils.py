from typing import Any, Dict, Optional

from blitzy_utils.common import blitzy_exponential_retry
from blitzy_utils.logger import logger
from msrest.authentication import BasicAuthentication

from azure.devops.connection import Connection
from azure.devops.exceptions import AzureDevOpsClientRequestError
from src.consts import AZURE_BASE_URL
from src.error.errors import AzureBaseError


@blitzy_exponential_retry()
def get_organization_info(organization_name: str, access_token: Optional[str] = None) -> Optional[Dict[str, Any]]:
    """
    Get Azure DevOps organization information with retry logic.

    :param organization_name: Name of the Azure DevOps organization
    :param access_token: Optional access token (if not provided, uses env var)
    :return: Dictionary containing organization information
    """
    try:
        # Use provided token or fall back to environment variable
        token = access_token
        if not token:
            raise ValueError("Access token is required for Azure DevOps authentication")

        # Create connection
        credentials = BasicAuthentication("", token)
        base_url = AZURE_BASE_URL or "https://dev.azure.com"
        organization_url = f"{base_url}/{organization_name}"

        connection = Connection(base_url=organization_url, creds=credentials)
        core_client = connection.clients.get_core_client()

        # Get organization and projects info
        projects = core_client.get_projects()

        if not projects:
            raise AzureBaseError(f"No projects found in organization '{organization_name}' or access denied")

        # Get organization details (Azure DevOps doesn't have a direct org info endpoint,
        # so we construct it from available information)
        return {
            organization_name: {
                "name": organization_name,
                "id": organization_name,  # Azure uses org name as identifier
                "type": "Organization",
                "url": organization_url,
                "projects_count": len(projects),
                "projects": [
                    {"name": project.name, "id": str(project.id)} for project in projects[:10]
                ],  # Limit to first 10
            }
        }

    except AzureDevOpsClientRequestError as e:
        if e.status_code == 401:
            logger.warning("Azure DevOps authentication failed")
            raise AzureBaseError("Azure DevOps authentication failed - invalid access token")
        elif e.status_code == 403:
            logger.warning("Azure DevOps access forbidden")
            raise AzureBaseError("Azure DevOps access forbidden - insufficient permissions")
        elif e.status_code == 404:
            logger.warning(f"Azure DevOps organization not found: {organization_name}")
            raise AzureBaseError(f"Azure DevOps organization '{organization_name}' not found")
        elif e.status_code == 429:
            logger.warning("Azure DevOps API rate limit exceeded")
            raise AzureBaseError("Azure DevOps API rate limit exceeded")
        else:
            error_message = f"Azure DevOps API error (status {e.status_code}): {str(e)}"
            logger.warning(error_message)
            raise AzureBaseError(error_message)

    except ValueError as e:
        logger.warning(f"Azure DevOps configuration error: {str(e)}")
        raise AzureBaseError(f"Azure DevOps configuration error: {str(e)}")

    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        raise Exception(f"Unexpected error occurred: {str(e)}")


@blitzy_exponential_retry()
def get_organization_projects(
    organization_name: str,
    access_token: Optional[str] = None,
) -> Optional[Dict[str, Any]]:
    """
    Get detailed project information for an Azure DevOps organization.

    :param organization_name: Name of the Azure DevOps organization
    :param access_token: Optional access token
    :return: Dictionary containing detailed project information
    """
    try:
        token = access_token
        if not token:
            raise ValueError("Access token is required for Azure DevOps authentication")

        credentials = BasicAuthentication("", token)
        base_url = AZURE_BASE_URL or "https://dev.azure.com"
        organization_url = f"{base_url}/{organization_name}"

        connection = Connection(base_url=organization_url, creds=credentials)
        core_client = connection.clients.get_core_client()
        git_client = connection.clients.get_git_client()

        # Get all projects
        projects = core_client.get_projects()

        project_details = {}
        for project in projects:
            try:
                # Get repositories for each project
                repos = git_client.get_repositories(project=project.name)

                project_details[project.name] = {
                    "name": project.name,
                    "id": str(project.id),
                    "description": getattr(project, "description", ""),
                    "url": getattr(project, "url", ""),
                    "repositories_count": len(repos),
                    "repositories": [{"name": repo.name, "id": str(repo.id)} for repo in repos[:5]],  # Limit to first 5
                }
            except Exception:
                # If we can't get repos for a project, still include the project info
                project_details[project.name] = {
                    "name": project.name,
                    "id": str(project.id),
                    "description": getattr(project, "description", ""),
                    "url": getattr(project, "url", ""),
                    "repositories_count": 0,
                    "repositories": [],
                }

        return {
            "organization": organization_name,
            "projects": project_details,
            "total_projects": len(projects),
        }

    except AzureDevOpsClientRequestError as e:
        if e.status_code == 401:
            raise AzureBaseError("Azure DevOps authentication failed - invalid access token")
        elif e.status_code == 403:
            raise AzureBaseError("Azure DevOps access forbidden - insufficient permissions")
        elif e.status_code == 404:
            raise AzureBaseError(f"Azure DevOps organization '{organization_name}' not found")
        elif e.status_code == 429:
            raise AzureBaseError("Azure DevOps API rate limit exceeded")
        else:
            error_message = f"Azure DevOps API error (status {e.status_code}): {str(e)}"
            raise AzureBaseError(error_message)

    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        raise Exception(f"Unexpected error occurred: {str(e)}")


def get_azure_projects_by_user_and_org(user_info, org_id):
    """
    Get Azure DevOps projects for a user and organization.

    Args:
        user_info: User information object with id and other details
        org_id: Azure DevOps organization ID

    Returns:
        List of project dictionaries with id, name, description, and other details
    """
    try:
        import requests
        from common_models.models import VersionControlSystem

        from src.service.azure_service import fetch_azure_secret_for_user

        logger.info(f"Getting Azure projects for user {user_info.id} in org {org_id}")

        # Get Azure access token for the user
        token_data = fetch_azure_secret_for_user(user_info.id, VersionControlSystem.AZURE_DEVOPS)
        access_token = token_data.accessToken

        # Call Azure DevOps API to get projects
        url = f"https://dev.azure.com/{org_id}/_apis/projects?api-version=6.0"
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json",
        }

        response = requests.get(url, headers=headers, timeout=30)
        response.raise_for_status()

        data = response.json()
        projects = []

        for project in data.get("value", []):
            project_data = {
                "id": project.get("id"),
                "name": project.get("name"),
                "description": project.get("description", ""),
                "url": project.get("url"),
                "state": project.get("state"),
                "visibility": project.get("visibility"),
            }
            projects.append(project_data)

        logger.info(f"Found {len(projects)} Azure projects for user {user_info.id} in org {org_id}")
        return projects

    except Exception as e:
        logger.error(f"Error retrieving Azure projects for user {user_info.id} in org {org_id}: {str(e)}")
        raise AzureBaseError(f"Failed to retrieve Azure projects: {str(e)}")


def get_azure_repos_by_user_and_org(user_info, org_id):
    """
    Get Azure DevOps repositories for a user and organization.

    Note: This function gets repositories across all projects in the organization.
    For project-specific repositories, use get_azure_repos_by_user_org_and_project.

    Args:
        user_info: User information object with id and other details
        org_id: Azure DevOps organization ID

    Returns:
        List of repository dictionaries with id, name, project, and other details
    """
    try:
        import requests
        from common_models.models import VersionControlSystem

        from src.service.azure_service import fetch_azure_secret_for_user

        logger.info(f"Getting Azure repositories for user {user_info.id} in org {org_id}")

        # Get Azure access token for the user
        token_data = fetch_azure_secret_for_user(user_info.id, VersionControlSystem.AZURE_DEVOPS)
        access_token = token_data.accessToken

        # First get all projects in the organization
        projects_url = f"https://dev.azure.com/{org_id}/_apis/projects?api-version=6.0"
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json",
        }

        projects_response = requests.get(projects_url, headers=headers, timeout=30)
        projects_response.raise_for_status()

        projects_data = projects_response.json()
        all_repositories = []

        # Get repositories from each project
        for project in projects_data.get("value", []):
            project_id = project.get("id")
            project_name = project.get("name")

            # Get repositories for this project
            repos_url = f"https://dev.azure.com/{org_id}/{project_id}/_apis/git/repositories?api-version=6.0"
            repos_response = requests.get(repos_url, headers=headers, timeout=30)
            repos_response.raise_for_status()

            repos_data = repos_response.json()

            for repo in repos_data.get("value", []):
                repo_data = {
                    "id": repo.get("id"),
                    "name": repo.get("name"),
                    "url": repo.get("url"),
                    "project": {"id": project_id, "name": project_name},
                    "defaultBranch": repo.get("defaultBranch"),
                    "size": repo.get("size", 0),
                }
                all_repositories.append(repo_data)

        logger.info(f"Found {len(all_repositories)} Azure repositories for user {user_info.id} in org {org_id}")
        return all_repositories

    except Exception as e:
        logger.error(f"Error retrieving Azure repositories for user {user_info.id} in org {org_id}: {str(e)}")
        raise AzureBaseError(f"Failed to retrieve Azure repositories: {str(e)}")


def get_azure_repos_by_user_org_and_project(user_info, org_id, project_id):
    """
    Get Azure DevOps repositories for a user, organization, and project.

    Args:
        user_info: User information object with id and other details
        org_id: Azure DevOps organization ID
        project_id: Azure DevOps project ID

    Returns:
        List of repository dictionaries with id, name, and other details
    """
    try:
        import requests
        from common_models.models import VersionControlSystem

        from src.service.azure_service import fetch_azure_secret_for_user

        logger.info(f"Getting Azure repositories for user {user_info.id} in org {org_id}, project {project_id}")

        # Get Azure access token for the user
        token_data = fetch_azure_secret_for_user(user_info.id, VersionControlSystem.AZURE_DEVOPS)
        access_token = token_data.accessToken

        # Call Azure DevOps API to get repositories for the specific project
        url = f"https://dev.azure.com/{org_id}/{project_id}/_apis/git/repositories?api-version=6.0"
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json",
        }

        response = requests.get(url, headers=headers, timeout=30)
        response.raise_for_status()

        data = response.json()
        repositories = []

        for repo in data.get("value", []):
            repo_data = {
                "id": repo.get("id"),
                "name": repo.get("name"),
                "url": repo.get("url"),
                "defaultBranch": repo.get("defaultBranch"),
                "size": repo.get("size", 0),
                "project": {"id": project_id},
            }
            repositories.append(repo_data)

        logger.info(
            f"Found {len(repositories)} Azure repositories for user {user_info.id} in org {org_id}, project {project_id}"
        )
        return repositories

    except Exception as e:
        logger.error(
            f"Error retrieving Azure repositories for user {user_info.id} in org {org_id}, project {project_id}: {str(e)}"
        )
        raise AzureBaseError(f"Failed to retrieve Azure repositories: {str(e)}")


def get_azure_branches_by_repo_id(user_info, org_id, project_id, repo_id):
    """
    Get Azure DevOps branches for a repository.

    Args:
        user_info: User information object with id and other details
        org_id: Azure DevOps organization ID
        project_id: Azure DevOps project ID
        repo_id: Azure DevOps repository ID

    Returns:
        List of branch dictionaries with name, commit info, and other details
    """
    try:
        import requests
        from common_models.models import VersionControlSystem

        from src.service.azure_service import fetch_azure_secret_for_user

        logger.info(
            f"Getting Azure branches for user {user_info.id} in org {org_id}, project {project_id}, repo {repo_id}"
        )

        # Get Azure access token for the user
        token_data = fetch_azure_secret_for_user(user_info.id, VersionControlSystem.AZURE_DEVOPS)
        access_token = token_data.accessToken

        # Call Azure DevOps API to get branches for the specific repository
        url = f"https://dev.azure.com/{org_id}/{project_id}/_apis/git/repositories/{repo_id}/refs?filter=heads/&api-version=6.0"
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json",
        }

        response = requests.get(url, headers=headers, timeout=30)
        response.raise_for_status()

        data = response.json()
        branches = []

        for ref in data.get("value", []):
            # Azure DevOps returns refs like "refs/heads/main", we want just "main"
            ref_name = ref.get("name", "")
            if ref_name.startswith("refs/heads/"):
                branch_name = ref_name[11:]  # Remove "refs/heads/" prefix

                branch_data = {
                    "name": branch_name,
                    "commit": {"sha": ref.get("objectId"), "url": ref.get("url")},
                    "protected": False,  # TODO: Add protection status if needed
                }
                branches.append(branch_data)

        logger.info(
            f"Found {len(branches)} Azure branches for user {user_info.id} in org {org_id}, project {project_id}, repo {repo_id}"
        )
        return branches

    except Exception as e:
        logger.error(
            f"Error retrieving Azure branches for user {user_info.id} in org {org_id}, project {project_id}, repo {repo_id}: {str(e)}"
        )
        raise AzureBaseError(f"Failed to retrieve Azure branches: {str(e)}")


def get_azure_branches_by_repo_with_context(user_info, org_id, project_id, repo_id):
    """
    Get Azure DevOps branches for a repository with full context.

    Args:
        user_info: User information object with id and other details
        org_id: Azure DevOps organization ID
        project_id: Azure DevOps project ID
        repo_id: Azure DevOps repository ID

    Returns:
        List of branch dictionaries with name, commit info, and other details
    """
    try:
        import requests
        from common_models.models import VersionControlSystem

        from src.service.azure_service import fetch_azure_secret_for_user

        logger.info(
            f"Getting Azure branches for user {user_info.id} in org {org_id}, project {project_id}, repo {repo_id}"
        )

        # Get Azure access token for the user
        token_data = fetch_azure_secret_for_user(user_info.id, VersionControlSystem.AZURE_DEVOPS)
        access_token = token_data.accessToken

        # Call Azure DevOps API to get branches for the specific repository
        url = f"https://dev.azure.com/{org_id}/{project_id}/_apis/git/repositories/{repo_id}/refs?filter=heads/&api-version=6.0"
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json",
        }

        response = requests.get(url, headers=headers, timeout=30)
        response.raise_for_status()

        data = response.json()
        branches = []

        for ref in data.get("value", []):
            # Azure DevOps returns refs like "refs/heads/main", we want just "main"
            ref_name = ref.get("name", "")
            if ref_name.startswith("refs/heads/"):
                branch_name = ref_name[11:]  # Remove "refs/heads/" prefix

                branch_data = {
                    "name": branch_name,
                    "commit": {"sha": ref.get("objectId"), "url": ref.get("url")},
                    "protected": False,  # TODO: Add protection status if needed
                }
                branches.append(branch_data)

        logger.info(
            f"Found {len(branches)} Azure branches for user {user_info.id} in org {org_id}, project {project_id}, repo {repo_id}"
        )
        return branches

    except Exception as e:
        logger.error(
            f"Error retrieving Azure branches for user {user_info.id} in org {org_id}, project {project_id}, repo {repo_id}: {str(e)}"
        )
        raise AzureBaseError(f"Failed to retrieve Azure branches: {str(e)}")
